import File3DIcon from "@shared/svg/3d-file.svg?react";
import DeleteIcon from "@shared/svg/delete.svg?react";
import LeaveByDoorIcon from "@shared/svg/leave-by-door.svg?react";
import { AlertBox } from "@shared/ui/AlertBox";
import BodyText, { BODY_TEXT_SIZES } from "@shared/ui/BodyText";
import Button, { BUTTON_VARIANTS } from "@shared/ui/Button";
import Drawer from "@shared/ui/Drawer";
import { SingleSelectDropdown } from "@shared/ui/Dropdowns";
import { RadioButtonInput, SearchInput } from "@shared/ui/Inputs";
import Label, { LABEL_SIZES } from "@shared/ui/Label";
import JSZip from "jszip";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useIntl as useInternationalization } from "react-intl";
import { AutoSizer, List as VirtualizedList } from "react-virtualized";
import { Waypoint } from "react-waypoint";

import AssemblyUploader from "../machine3DTab/_assemblyUploader";

import ContentLoading from "~/components/ContentLoading";
import ProgressBar from "~/components/form/_progressBar";
import { useAuth } from "~/components/general";
import AttachmentUploader from "~/components/machine3DTab/_attachmentUploader";
import TemplateDropdown from "~/components/machines/TemplateDropdown";
import {
  COLOR,
  ITEMS_PER_PAGE,
  THREE_D_MODEL_EVENTS,
  UPLOAD_3D_MODEL,
  UPLOAD_3D_MODEL_ASSEMBLY,
} from "~/constants";
import { useLargeFilesUpload, useUploadFiles } from "~/hooks";
import { ExclamationIcon } from "~/icons/icons";
import { useAddAsset3DModel, useListAllOwnOemAssets } from "~/services/asset";
import { useAddAssetTemplate3DModel } from "~/services/assetTemplate";
import type { AssetTemplate } from "~/types/assetTemplate";
import type { FileUploaderType } from "~/types/fileUpload";
import type { UserType } from "~/types/user";
import type { WaypointEventType } from "~/types/waypointEvent";
import { registerMixpanelEvent } from "~/utils/_mixpanel";
import { upload3DFile } from "~/utils/fileUploader";

type MachineDataType = {
  label: string;
  serialNumber: string;
  _id: string;
  oem: {
    _id: string;
  };
};

type File3DType = File & {
  path: string;
};

type StateType = {
  drawerOpen: boolean;
  machineData: MachineDataType;
  productData: AssetTemplate;
  searchQuery: string;
  isFileUploading: boolean;
  showQuitAlert: boolean;
  file: File3DType;
  selectedMainAssemblyFile?: string;
};

const MODEL_CREATION_OPTIONS = {
  ASSET: "ASSET",
  PRODUCT: "PRODUCT",
};

const MODEL_TYPE_OPTIONS = {
  MODEL: "MODEL",
  ASSEMBLY: "ASSEMBLY",
};

const AddThreeDModel = ({
  machineListRefetch,
  is3DModelPaid,
  onAdd,
}: {
  machineListRefetch: () => void;
  is3DModelPaid: boolean;
  onAdd: (id: string) => void;
}) => {
  const { messages } = useInternationalization();
  const { user } = useAuth() as { user: UserType };

  const CREATE_OPTIONS = [
    {
      heading: messages?.["machines"]?.["myAssets"],
      value: MODEL_CREATION_OPTIONS.ASSET,
    },
    {
      heading: messages?.["machines"]?.["myProducts"],
      value: MODEL_CREATION_OPTIONS.PRODUCT,
    },
  ];

  const TYPE_OPTIONS = [
    {
      heading: messages?.["machines"]?.["singleModelTitle"],
      name: messages?.["machines"]?.["singleModelDescription"],
      value: MODEL_TYPE_OPTIONS.MODEL,
    },
    {
      heading: messages?.["machines"]?.["assemblyModelTitle"],
      name: messages?.["machines"]?.["assemblyModelDescription"],
      value: MODEL_TYPE_OPTIONS.ASSEMBLY,
    },
  ];

  const [state, setState] = React.useState<StateType>({
    drawerOpen: false,
    machineData: null,
    productData: null,
    searchQuery: "",
    isFileUploading: false,
    showQuitAlert: false,
    file: null,
    selectedMainAssemblyFile: null,
  });

  const { assetsListLoading: loadingAssetCount, totalCount: totalAssetCount } =
    useListAllOwnOemAssets({
      limit: 1,
      searchQuery: "",
      skip: 0,
      skipCondition: !is3DModelPaid,
    });
  const {
    assets,
    handleFetchMore: handleAssetsFetchMore,
    assetsListLoading,
    assetsRefetch,
    totalCount: assetsTotalCount,
  } = useListAllOwnOemAssets({
    limit: ITEMS_PER_PAGE,
    searchQuery: state.searchQuery,
    skip: 0,
    where: {
      includeNon3DModelAssetsOnly: true,
    },
    skipCondition: !is3DModelPaid,
  });

  const loadingTotalCount = loadingAssetCount;
  const actualTotalCount = totalAssetCount;
  const resources = assets;
  const handleFetchMore = handleAssetsFetchMore;
  const refetch = assetsRefetch;
  const loading = assetsListLoading;
  const totalCount = assetsTotalCount;

  const [modelCreation, setModelCreation] = React.useState(
    MODEL_CREATION_OPTIONS.ASSET,
  );

  const [modelType, setModelType] = React.useState(MODEL_TYPE_OPTIONS.MODEL);

  const handleScrollBottom = (event: WaypointEventType) =>
    resources.length >= ITEMS_PER_PAGE &&
    !loading &&
    event.previousPosition !== Waypoint.above &&
    totalCount > resources.length &&
    handleFetchMore({
      limit: ITEMS_PER_PAGE,
      skip: resources.length,
    });

  const handleSearchQuery = React.useCallback(
    (searchQuery: string) =>
      setState((prevState) => ({
        ...prevState,
        searchQuery,
      })),
    [],
  );

  const fileUploader = useUploadFiles();
  const {
    fileUploader: largeFileUploader,
    progress,
    isLoading,
  } = useLargeFilesUpload();

  const { addAsset3DModel } = useAddAsset3DModel();
  const { addAssetTemplate3DModel } = useAddAssetTemplate3DModel();

  const handleUpload = async (file: File3DType) => {
    registerMixpanelEvent(UPLOAD_3D_MODEL);
    setState((prevState) => ({
      ...prevState,
      isFileUploading: false,
      file,
    }));
  };

  const handleAssemblyUpload = async (file: File3DType) => {
    registerMixpanelEvent(UPLOAD_3D_MODEL_ASSEMBLY);
    setState((prevState) => ({
      ...prevState,
      isFileUploading: false,
      file,
    }));
  };

  const onSubmit = async () => {
    registerMixpanelEvent(THREE_D_MODEL_EVENTS.CREATE_MODEL, {
      oemUrl: window.location.pathname,
      oemName: user.oem.name,
      oemUserName: user.name,
      creationDate: new Date().toISOString(),
      accessLevel: user.role,
    });
    setState((prevState) => ({
      ...prevState,
      isFileUploading: true,
    }));
    const _3dModelUrl = await upload3DFile(
      state.file,
      modelCreation === MODEL_CREATION_OPTIONS.ASSET
        ? state.machineData?.oem?._id
        : user?.oem?._id,
      modelCreation === MODEL_CREATION_OPTIONS.ASSET
        ? state.machineData?._id
        : // @ts-ignore
          state.productData?.value,
      largeFileUploader,
      fileUploader as FileUploaderType,
      (val: boolean) =>
        setState((prevState) => ({
          ...prevState,
          isFileUploading: val,
        })),
      modelCreation === MODEL_CREATION_OPTIONS.PRODUCT,
    );

    const getMainFileUrl = () => {
      const splitUrl = _3dModelUrl.split("/");
      splitUrl.pop();
      const parentUrl = splitUrl.join("/");
      const mainFileUrl = `${parentUrl}/${state.selectedMainAssemblyFile}`;
      return mainFileUrl;
    };

    const url =
      modelType === MODEL_TYPE_OPTIONS.MODEL ? _3dModelUrl : getMainFileUrl();

    await (modelCreation === MODEL_CREATION_OPTIONS.ASSET
      ? addAsset3DModel
      : addAssetTemplate3DModel)({
      _id:
        modelCreation === MODEL_CREATION_OPTIONS.ASSET
          ? state.machineData?._id
          : // @ts-ignore
            state.productData?.value,
      _3dModelUrl: url,
    });
    machineListRefetch();
    setState((prevState) => ({
      ...prevState,
      drawerOpen: false,
      file: null,
      selectedMainAssemblyFile: null,
      isFileUploading: false,
    }));
    onAdd(state.machineData._id);
  };

  return (
    <>
      <Button
        variant={BUTTON_VARIANTS.PRIMARY}
        disabled={false}
        onClick={() => {
          setState((prevState) => ({
            ...prevState,
            drawerOpen: true,
          }));
          if (resources) {
            refetch();
          }
        }}
        className="flex-shrink-0"
        text={messages.machines["new3DModelButton"]}
      />
      <AlertBox
        title={messages?.tickets?.["ticketQuitAlertTitle"]}
        description={messages?.tickets?.["ticketQuitAlertMessage"]}
        isOpen={state.showQuitAlert}
        acceptButtonText={messages?.common?.["quit"]}
        cancelButtonText={messages?.common?.["cancel"]}
        image={<LeaveByDoorIcon width="130" height="130" />}
        onCancel={() =>
          setState((prevState) => ({
            ...prevState,
            showQuitAlert: false,
          }))
        }
        onAccept={() =>
          setState((prevState) => ({
            ...prevState,
            drawerOpen: false,
            file: null,
            machineData: null,
            showQuitAlert: false,
          }))
        }
        overlay={false}
        zIndexClass="z-30"
      />

      <Drawer
        contentClassName="flex flex-col space-y-2xl"
        zIndexClass="z-20"
        isOpen={state.drawerOpen}
        onClose={() =>
          setState((prevState) => ({
            ...prevState,
            showQuitAlert: !!(state.machineData || state.file),
            drawerOpen: !!(state.machineData || state.file),
          }))
        }
        onSubmit={onSubmit}
        submitButtonDisabled={
          (!state.machineData && !state.productData) ||
          !state.file ||
          state.isFileUploading ||
          (modelType === MODEL_TYPE_OPTIONS.ASSEMBLY &&
            !state.selectedMainAssemblyFile)
        }
        submitButtonText={messages.machines["submitModel"]}
        title={messages.machines["new3DModelButton"]}
      >
        <div className="flex space-x-sm py-sm px-md bg-brand-lightest rounded-lg">
          <ExclamationIcon
            strokeColor="stroke-brand"
            className="flex-shrink-0"
          />
          <BodyText size={BODY_TEXT_SIZES.X_SMALL}>
            {messages.machines?.["modelCreation"]}
          </BodyText>
        </div>
        <RadioButtonInput
          keyId="createModel"
          options={CREATE_OPTIONS}
          value={modelCreation}
          onChange={(e) => {
            const value = e?.target?.value;
            setModelCreation(value);
          }}
        />
        {modelCreation === MODEL_CREATION_OPTIONS.ASSET ? (
          <SingleSelectDropdown
            disabled={loadingTotalCount || actualTotalCount === 0}
            keyId="select-machine"
            label={`${messages.machines?.["selectAsset"]} *`}
            onChange={(value: Array<MachineDataType>) =>
              setState((prevState) => ({
                ...prevState,
                machineData: value.find(Boolean),
              }))
            }
            isAsync={true}
            asyncProps={{
              isLoading: loadingTotalCount,
              onScrollToBottom: handleScrollBottom,
              onSearch: handleSearchQuery,
            }}
            options={resources.map(
              ({ _id, name, serialNumber, oem, image }) => ({
                label: name,
                description: serialNumber,
                _id: _id,
                oem: oem,
                value: _id,
                thumbnail: image ?? "default",
              }),
            )}
            placeholder={messages.machines?.["chooseAsset"]}
            searchable
            searchBy="label, serialNumber"
            // @ts-ignore
            values={state.machineData?.label}
          />
        ) : (
          <TemplateDropdown
            oemId={user.oem?._id}
            onChange={(val) =>
              setState((prev) => ({ ...prev, productData: val }))
            }
            filter={{
              includeNon3DModelAssetsOnly: true,
            }}
          />
        )}
        <RadioButtonInput
          keyId="selectModelType"
          label={`${messages.machines?.["modelType"]} *`}
          nameStyle={{ width: "180px" }}
          options={TYPE_OPTIONS}
          value={modelType}
          onChange={(e) => {
            if (state.isFileUploading) return;
            const value = e?.target?.value;
            setModelType(value);
            setState((prevState) => ({
              ...prevState,
              file: null,
              selectedMainAssemblyFile: null,
            }));
          }}
        />
        {modelType === MODEL_TYPE_OPTIONS.MODEL ? (
          <div className="space-y-sm">
            <BodyText color="text-secondary" size={BODY_TEXT_SIZES.X_SMALL}>
              {messages.machines?.["uploadModel"]} *
            </BodyText>
            {isLoading || state.isFileUploading ? (
              <div className="threedy-upload-progress-container">
                <BodyText color="text-primary" size={BODY_TEXT_SIZES.SMALL}>
                  {messages.common?.["uploadingFile"]}
                </BodyText>
                <ProgressBar
                  bgcolor={COLOR.$blue_v2_100}
                  completed={state.isFileUploading ? 50 : progress}
                  progressBarFillerClassName="threedy-upload-progress-bar-filler"
                />
              </div>
            ) : state.file ? (
              <div className="flex justify-between py-md px-sm border-solid rounded-lg border-[1px] border-b border-primary items-center">
                <div className="flex space-x-md">
                  <File3DIcon className="w-3xl h-3xl" />
                  <div className="flex flex-col">
                    <Label size={LABEL_SIZES.SMALL}>{state.file?.path}</Label>
                    <Label size={LABEL_SIZES.X_SMALL} color="text-secondary">
                      {(state.file?.size / 1024 / 1024)?.toFixed(0)} MB
                    </Label>
                  </div>
                </div>
                <DeleteIcon
                  className="text-secondary w-lg h-lg cursor-pointer"
                  onClick={() =>
                    setState((prevState) => ({
                      ...prevState,
                      file: null,
                      selectedMainAssemblyFile: null,
                    }))
                  }
                />
              </div>
            ) : (
              <div
                className="border-primary border-dashed rounded-lg py-lg relative overflow-hidden"
                style={{ minHeight: "270px" }}
              >
                {!state.machineData && !state.productData && (
                  <div
                    className="absolute w-full flex justify-center items-center"
                    style={{ height: "80%" }}
                  />
                )}
                <AttachmentUploader
                  onUpload={handleUpload}
                  is3DModelDrawer={true}
                />
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-sm">
            <BodyText color="text-secondary" size={BODY_TEXT_SIZES.X_SMALL}>
              {messages.machines?.["uploadModel"]} *
            </BodyText>
            {isLoading || state.isFileUploading ? (
              <div className="threedy-upload-progress-container">
                <BodyText color="text-primary" size={BODY_TEXT_SIZES.SMALL}>
                  {messages.common?.["uploadingFile"]}
                </BodyText>
                <ProgressBar
                  bgcolor={COLOR.$blue_v2_100}
                  completed={state.isFileUploading ? 50 : progress}
                  progressBarFillerClassName="threedy-upload-progress-bar-filler"
                />
              </div>
            ) : state.file ? (
              <div className="flex flex-col space-y-2xl">
                <div className="flex justify-between py-md px-sm border-solid rounded-lg border-[1px] border-b border-primary items-center">
                  <div className="flex space-x-md">
                    <File3DIcon className="w-3xl h-3xl" />
                    <div className="flex flex-col">
                      <Label size={LABEL_SIZES.SMALL}>{state.file?.path}</Label>
                      <Label size={LABEL_SIZES.X_SMALL} color="text-secondary">
                        {(state.file?.size / 1024 / 1024)?.toFixed(0)} MB
                      </Label>
                    </div>
                  </div>
                  <DeleteIcon
                    className="text-secondary w-lg h-lg cursor-pointer"
                    onClick={() =>
                      setState((prevState) => ({
                        ...prevState,
                        file: null,
                        selectedMainAssemblyFile: null,
                      }))
                    }
                  />
                </div>
                <BodyText color="text-secondary" size={BODY_TEXT_SIZES.X_SMALL}>
                  {messages.machines?.["selectParentFile"]} *
                </BodyText>
                <ZipFilePreview
                  inputFile={state.file}
                  onMainFileSelect={(selectedFile: string) => {
                    setState((prevState) => ({
                      ...prevState,
                      selectedMainAssemblyFile: selectedFile,
                    }));
                  }}
                />
              </div>
            ) : (
              <div
                className="border-primary border-dashed rounded-lg py-lg relative overflow-hidden"
                style={{ minHeight: "270px" }}
              >
                {!state.machineData && !state.productData && (
                  <div
                    className="absolute w-full flex justify-center items-center"
                    style={{ height: "80%" }}
                  />
                )}
                <AssemblyUploader onUpload={handleAssemblyUpload} />
              </div>
            )}
          </div>
        )}
      </Drawer>
    </>
  );
};

const ZipFilePreview = ({
  inputFile,
  onMainFileSelect,
}: {
  inputFile: File;
  onMainFileSelect?: (selectedFile: string) => void;
}) => {
  const [filePaths, setFilePaths] = useState<string[]>([]);
  const [selectedMainFile, setSelectedMainFile] = useState<string>(null);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const { messages } = useInternationalization();

  const isValidFile = (path: string): boolean => {
    if (!path) return false;

    const fileName = path.split("/").pop();
    if (!fileName) return false;

    const parts = fileName.split(".");

    return (
      parts.length >= 2 &&
      parts[0].length > 0 &&
      parts[parts.length - 1].length > 0
    );
  };

  const processZipInChunks = useCallback(
    async (zip: JSZip): Promise<string[]> => {
      const fileEntries = Object.entries(zip.files);
      const validPaths: string[] = [];
      const chunkSize = 50; // Process files in chunks to avoid blocking UI

      for (let i = 0; i < fileEntries.length; i += chunkSize) {
        const chunk = fileEntries.slice(i, i + chunkSize);

        const chunkPaths = chunk
          .filter(([path, file]) => !file.dir && isValidFile(path))
          .map(([path]) => path);

        validPaths.push(...chunkPaths);

        if (i + chunkSize < fileEntries.length) {
          await new Promise((resolve) => setTimeout(resolve, 0));
        }
      }

      return validPaths;
    },
    [],
  );

  const handleZipChange = useCallback(async () => {
    const file = inputFile;

    if (!file || file.type !== "application/zip") return;

    setIsLoading(true);
    setError(null);
    setFilePaths([]);

    try {
      const zip = new JSZip();
      const arrayBuffer = await file.arrayBuffer();

      const contents = await zip.loadAsync(arrayBuffer, {
        checkCRC32: false,
      });

      const validPaths = await processZipInChunks(contents);

      setFilePaths(validPaths);
    } catch (err) {
      setError(messages.machines?.["zipFilePreviewErrorMessage"]);
    } finally {
      setIsLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [inputFile, processZipInChunks]);

  const handleMainFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.value;
    setSelectedMainFile(selectedFile);
    onMainFileSelect?.(selectedFile);
  };

  useEffect(() => {
    if (inputFile) {
      handleZipChange();
    }
  }, [inputFile, handleZipChange]);

  const filteredFilePaths = useMemo(() => {
    return filePaths.filter((path) =>
      path.toLowerCase().includes(searchQuery.toLowerCase()),
    );
  }, [filePaths, searchQuery]);

  const RadioOption = ({
    path,
    isSelected,
    onChange,
  }: {
    path: string;
    isSelected: boolean;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  }) => {
    const radioInputClasses: string =
      "m-0 appearance-none w-lg h-lg cursor-pointer rounded-full bg-primary border-solid";
    return (
      <div key={path} className="flex items-center gap-sm">
        <input
          type="radio"
          id={`mainAssemblyFileSelection-${path}`}
          name="mainAssemblyFileSelection"
          value={path}
          onChange={onChange}
          checked={isSelected}
          className={`${radioInputClasses} ${
            isSelected ? "border-accent border-4" : "border-secondary border"
          }`}
        />
        <div className="flex flex-col gap-2xs items-start w-fit">
          <label
            htmlFor={`mainAssemblyFileSelection-${path}`}
            className="font-manrope font-bold text-sm cursor-pointer text-primary"
            style={{ wordBreak: "break-word" }}
          >
            {path}
          </label>
        </div>
      </div>
    );
  };

  const rowRenderer = ({
    index,
    key,
    style,
  }: {
    index: number;
    key: string;
    style: React.CSSProperties;
  }) => {
    const path = filteredFilePaths[index];
    return (
      <div key={key} style={style}>
        <RadioOption
          path={path}
          isSelected={selectedMainFile === path}
          onChange={handleMainFileChange}
        />
      </div>
    );
  };

  if (error) {
    return (
      <div className="flex flex-col align-center justify-center h-9xl">
        <BodyText
          size={BODY_TEXT_SIZES.X_SMALL}
          color="text-secondary text-center"
        >
          {messages.machines?.["zipFilePreviewErrorMessage"]}
        </BodyText>
      </div>
    );
  }

  if (!isLoading) {
    return (
      <div className="flex flex-col space-y-md">
        <div className="flex items-center justify-center py-lg">
          <div className="flex items-center space-x-sm">
            <ContentLoading />
            <BodyText size={BODY_TEXT_SIZES.SMALL} color="text-secondary">
              Processing zip file...
            </BodyText>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col">
      {filePaths.length === 0 ? (
        <BodyText size={BODY_TEXT_SIZES.SMALL} color="text-secondary">
          {messages.machines?.["emptyZipFileErrorMessage"]}
        </BodyText>
      ) : (
        <>
          <SearchInput
            placeholder="Search assembly files"
            value={searchQuery}
            onChange={setSearchQuery}
            className="!w-full !mt-0 !mb-2xl"
            activeByDefault={true}
          />
          {filteredFilePaths.length === 0 ? (
            <BodyText size={BODY_TEXT_SIZES.SMALL} color="text-secondary">
              No files match your search criteria.
            </BodyText>
          ) : (
            <div className="border border-secondary rounded-md">
              <div
                style={{ height: Math.min(400, filteredFilePaths.length * 48) }}
              >
                <AutoSizer>
                  {({ height, width }) => (
                    <VirtualizedList
                      height={height}
                      width={width}
                      rowCount={filteredFilePaths.length}
                      rowHeight={48}
                      rowRenderer={rowRenderer}
                      overscanRowCount={5}
                    />
                  )}
                </AutoSizer>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default AddThreeDModel;
